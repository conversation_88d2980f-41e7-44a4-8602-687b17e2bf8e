#!/usr/bin/env python3
"""
启动BabelDOC Web前端的脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🌍 启动BabelDOC Web前端...")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return 1
    
    # 检查是否在正确的目录
    if not Path("web_frontend/app.py").exists():
        print("❌ 错误: 找不到web_frontend/app.py")
        print("请确保在BabelDOC项目根目录运行此脚本")
        return 1
    
    # 创建必要的目录
    os.makedirs("web_frontend/uploads", exist_ok=True)
    os.makedirs("web_frontend/outputs", exist_ok=True)
    print("✅ 创建必要目录")
    
    # 检查并安装前端依赖
    try:
        print("📦 检查前端依赖...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "fastapi>=0.104.0", 
            "uvicorn[standard]>=0.24.0",
            "python-multipart>=0.0.6",
            "websockets>=12.0"
        ], check=True, capture_output=True)
        print("✅ 前端依赖已安装")
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装前端依赖失败: {e}")
        return 1
    
    # 启动服务器
    print("\n🚀 启动Web服务器...")
    print("📍 访问地址: http://localhost:8000")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        # 切换到web_frontend目录
        os.chdir("web_frontend")
        
        # 启动uvicorn服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n\n⏹️  服务器已停止")
        return 0
    except Exception as e:
        print(f"\n❌ 启动服务器失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
