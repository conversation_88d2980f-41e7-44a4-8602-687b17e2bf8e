# BabelDOC Web前端使用指南

我已经为您的BabelDOC PDF翻译项目创建了一套完整的Web前端界面。这个前端提供了用户友好的图形界面，支持文件上传、实时进度监控和结果下载。

## 📁 文件结构

```
web_frontend/
├── app.py                 # 完整版FastAPI后端服务器
├── simple_app.py          # 简化版测试服务器
├── requirements.txt       # Python依赖
├── start_server.bat       # Windows启动脚本
├── README.md             # 详细说明文档
├── static/               # 前端静态文件
│   ├── index.html        # 主页面
│   ├── style.css         # 样式文件
│   └── script.js         # 前端JavaScript
├── uploads/              # 上传文件存储目录
└── outputs/              # 翻译结果存储目录

start_web_frontend.py     # 项目根目录的启动脚本
WEB_FRONTEND_GUIDE.md     # 本指南文件
```

## 🚀 快速开始

### 方法1: 使用简化版测试服务器

1. **安装依赖**
   ```bash
   pip install fastapi uvicorn python-multipart websockets
   ```

2. **启动服务器**
   ```bash
   cd web_frontend
   python simple_app.py
   ```

3. **访问界面**
   打开浏览器访问: http://localhost:8000

### 方法2: 使用完整版服务器

1. **运行启动脚本**
   ```bash
   python start_web_frontend.py
   ```

2. **或者手动启动**
   ```bash
   cd web_frontend
   pip install -r requirements.txt
   python app.py
   ```

## 🌟 功能特性

### 1. 文件上传
- ✅ 支持拖拽上传PDF文件
- ✅ 文件类型验证（仅支持PDF）
- ✅ 文件大小限制（最大100MB）
- ✅ 上传进度显示

### 2. 翻译配置
- ✅ 目标语言选择（中文、英文、日文等）
- ✅ 源语言选择（支持自动检测）
- ✅ AI模型选择（Qwen3-32B、GPT-4等）
- ✅ API配置（地址、密钥）

### 3. 实时进度监控
- ✅ WebSocket实时通信
- ✅ 整体进度百分比显示
- ✅ 当前处理阶段显示
- ✅ 阶段内进度显示
- ✅ 状态消息更新

### 4. 结果展示
- ✅ 翻译完成通知
- ✅ 处理时间统计
- ✅ 多文件下载支持
- ✅ 双语版本和纯翻译版本

### 5. 用户体验
- ✅ 响应式设计（支持移动设备）
- ✅ 现代化UI界面
- ✅ 动画效果和过渡
- ✅ 错误处理和提示

## 📊 翻译进度阶段

前端会实时显示以下8个翻译阶段的进度：

1. **ILCreater** - 创建中间语言表示
2. **LayoutParser** - 解析文档布局
3. **ParagraphFinder** - 识别段落结构
4. **StylesAndFormulas** - 处理样式和公式
5. **ILTranslator** - 执行文本翻译
6. **Typesetting** - 排版处理
7. **FontMapper** - 字体映射
8. **PDFCreater** - 生成最终PDF

## 🔧 技术架构

### 后端 (FastAPI)
- **异步处理**: 使用asyncio处理翻译任务
- **WebSocket**: 实时进度推送
- **文件管理**: 上传和下载文件处理
- **BabelDOC集成**: 调用原有翻译引擎

### 前端 (HTML/CSS/JavaScript)
- **现代化界面**: 使用CSS Grid和Flexbox布局
- **实时通信**: WebSocket接收进度更新
- **状态管理**: JavaScript管理应用状态
- **响应式设计**: 适配不同屏幕尺寸

## 🛠️ 配置说明

### 默认配置
```python
# 服务器配置
HOST = "0.0.0.0"
PORT = 8000

# 文件配置
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
UPLOAD_DIR = "uploads/"
OUTPUT_DIR = "outputs/"

# API配置（默认值）
DEFAULT_API_URL = "http://***********:3000/v1/"
DEFAULT_MODEL = "qwen3-32b"
DEFAULT_API_KEY = "sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG"
```

### 自定义配置
您可以在前端界面中修改以下配置：
- API服务地址
- API密钥
- 翻译模型
- 源语言和目标语言

## 🔍 使用流程

1. **上传PDF文件**
   - 拖拽文件到上传区域或点击选择
   - 系统验证文件格式和大小

2. **配置翻译参数**
   - 选择目标语言
   - 配置API设置
   - 选择AI模型

3. **开始翻译**
   - 点击"开始翻译"按钮
   - 系统上传文件并启动翻译任务

4. **监控进度**
   - 实时查看翻译进度
   - 了解当前处理阶段

5. **下载结果**
   - 翻译完成后下载结果文件
   - 支持多种格式（双语版、纯翻译版）

## 🐛 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查Python版本
   python --version  # 需要3.8+
   
   # 安装依赖
   pip install fastapi uvicorn python-multipart websockets
   ```

2. **文件上传失败**
   - 检查文件格式是否为PDF
   - 确认文件大小不超过100MB
   - 检查网络连接

3. **翻译失败**
   - 验证API配置是否正确
   - 检查API服务是否可访问
   - 查看服务器日志获取详细错误信息

4. **进度不更新**
   - 检查WebSocket连接
   - 刷新页面重新连接
   - 查看浏览器控制台错误

### 调试方法

1. **查看服务器日志**
   ```bash
   # 启动时会显示详细日志
   python app.py
   ```

2. **浏览器调试**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息
   - 检查Network标签页的网络请求

## 🔮 扩展功能

可以考虑添加的功能：

1. **用户管理**
   - 用户注册和登录
   - 翻译历史记录
   - 个人设置保存

2. **批量处理**
   - 多文件同时上传
   - 批量翻译队列
   - 进度汇总显示

3. **高级配置**
   - 更多翻译参数
   - 自定义词汇表
   - 翻译质量设置

4. **结果管理**
   - 在线预览PDF
   - 翻译结果对比
   - 导出多种格式

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看本指南的故障排除部分
2. 检查服务器日志和浏览器控制台
3. 确认BabelDOC主项目功能正常
4. 验证API服务配置正确

## 📄 许可证

本Web前端遵循与BabelDOC主项目相同的许可证。

---

**祝您使用愉快！** 🎉

如果您需要任何帮助或有改进建议，请随时联系。
