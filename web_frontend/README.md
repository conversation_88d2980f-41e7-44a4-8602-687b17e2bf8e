# BabelDOC Web Frontend

这是BabelDOC PDF翻译工具的Web前端界面，提供了用户友好的图形界面来进行PDF翻译。

## 功能特性

- 📁 **文件上传**: 支持拖拽上传PDF文件
- ⚙️ **参数配置**: 可配置翻译语言、AI模型、API设置等
- 📊 **实时进度**: 通过WebSocket实时显示翻译进度
- 📥 **结果下载**: 翻译完成后可直接下载结果文件
- 📱 **响应式设计**: 支持桌面和移动设备

## 安装和运行

### 1. 安装依赖

```bash
# 进入web_frontend目录
cd web_frontend

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
# 方式1: 直接运行
python app.py

# 方式2: 使用uvicorn
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问界面

打开浏览器访问: http://localhost:8000

## 使用说明

### 1. 上传PDF文件
- 点击"选择文件"按钮或直接拖拽PDF文件到上传区域
- 支持最大100MB的PDF文件

### 2. 配置翻译参数
- **目标语言**: 选择要翻译到的目标语言
- **源语言**: 选择源文件语言（可选择自动检测）
- **AI模型**: 选择使用的AI翻译模型
- **API地址**: 配置OpenAI兼容的API服务地址
- **API密钥**: 输入对应的API密钥

### 3. 开始翻译
- 点击"开始翻译"按钮启动翻译任务
- 系统会实时显示翻译进度，包括：
  - 整体进度百分比
  - 当前处理阶段
  - 阶段内进度

### 4. 下载结果
- 翻译完成后会显示下载链接
- 通常包含双语版本和纯翻译版本的PDF文件

## 翻译阶段说明

翻译过程包含以下8个主要阶段：

1. **ILCreater**: 创建中间语言表示
2. **LayoutParser**: 解析文档布局
3. **ParagraphFinder**: 识别段落结构
4. **StylesAndFormulas**: 处理样式和公式
5. **ILTranslator**: 执行文本翻译
6. **Typesetting**: 排版处理
7. **FontMapper**: 字体映射
8. **PDFCreater**: 生成最终PDF

## 技术架构

### 后端 (FastAPI)
- **文件上传**: 处理PDF文件上传和存储
- **翻译任务**: 集成BabelDOC翻译引擎
- **WebSocket**: 实时进度推送
- **文件服务**: 提供翻译结果下载

### 前端 (HTML/CSS/JavaScript)
- **响应式界面**: 适配不同设备屏幕
- **拖拽上传**: 支持文件拖拽操作
- **实时更新**: WebSocket接收进度更新
- **状态管理**: 管理翻译任务状态

## 配置说明

### 默认配置
- **服务端口**: 8000
- **上传目录**: `web_frontend/uploads/`
- **输出目录**: `web_frontend/outputs/`
- **最大文件大小**: 100MB

### API配置
默认配置使用本地部署的Qwen模型：
- **API地址**: http://***********:3000/v1/
- **模型**: qwen3-32b
- **API密钥**: sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG

可以根据实际情况修改这些配置。

## 故障排除

### 常见问题

1. **上传失败**
   - 检查文件格式是否为PDF
   - 确认文件大小不超过100MB
   - 检查网络连接

2. **翻译失败**
   - 验证API配置是否正确
   - 检查API密钥是否有效
   - 确认API服务是否可访问

3. **进度不更新**
   - 检查WebSocket连接是否正常
   - 刷新页面重新连接

4. **下载失败**
   - 确认翻译已完成
   - 检查输出文件是否存在

### 日志查看

服务器日志会显示详细的错误信息，可以通过以下方式查看：

```bash
# 启动时查看日志
python app.py

# 或使用uvicorn查看详细日志
uvicorn app:app --host 0.0.0.0 --port 8000 --log-level debug
```

## 开发说明

### 项目结构
```
web_frontend/
├── app.py              # FastAPI后端服务器
├── requirements.txt    # Python依赖
├── README.md          # 说明文档
├── static/            # 静态文件
│   ├── index.html     # 主页面
│   ├── style.css      # 样式文件
│   └── script.js      # 前端脚本
├── uploads/           # 上传文件存储
└── outputs/           # 翻译结果存储
```

### 扩展功能

可以考虑添加的功能：
- 用户认证和会话管理
- 翻译历史记录
- 批量文件处理
- 更多翻译参数配置
- 翻译质量评估

## 许可证

本项目遵循与BabelDOC主项目相同的许可证。
