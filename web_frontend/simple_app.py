"""
简化版的BabelDOC Web前端，用于测试基本功能
"""

import asyncio
import json
import logging
import os
import uuid
from pathlib import Path
from typing import Dict

try:
    from fastapi import FastAPI, File, Form, HTTPException, UploadFile, WebSocket, WebSocketDisconnect
    from fastapi.responses import FileResponse, HTMLResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn
except ImportError:
    print("请先安装FastAPI和相关依赖:")
    print("pip install fastapi uvicorn python-multipart websockets")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="BabelDOC Web Frontend (Simple)", version="1.0.0")

# 确保目录存在
os.makedirs("uploads", exist_ok=True)
os.makedirs("outputs", exist_ok=True)

# 如果static目录存在，则挂载静态文件
if Path("static").exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局变量
translation_tasks: Dict[str, asyncio.Task] = {}

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, task_id: str):
        await websocket.accept()
        self.active_connections[task_id] = websocket

    def disconnect(self, task_id: str):
        if task_id in self.active_connections:
            del self.active_connections[task_id]

    async def send_message(self, task_id: str, message: dict):
        if task_id in self.active_connections:
            try:
                await self.active_connections[task_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {task_id}: {e}")
                self.disconnect(task_id)

manager = ConnectionManager()

@app.get("/")
async def read_root():
    """提供简单的测试页面"""
    if Path("static/index.html").exists():
        return FileResponse("static/index.html")
    else:
        return HTMLResponse("""
        <!DOCTYPE html>
        <html>
        <head>
            <title>BabelDOC Web Frontend - Test</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 600px; margin: 0 auto; }
                .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
                .form-group { margin: 15px 0; }
                label { display: block; margin-bottom: 5px; }
                input, select { width: 100%; padding: 8px; margin-bottom: 10px; }
                button { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
                button:hover { background: #0056b3; }
                .progress { margin: 20px 0; }
                .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; }
                .progress-fill { height: 100%; background: #007bff; border-radius: 10px; width: 0%; transition: width 0.3s; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🌍 BabelDOC Web Frontend (测试版)</h1>
                
                <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                    <p>点击选择PDF文件</p>
                    <input type="file" id="fileInput" accept=".pdf" style="display: none;">
                </div>
                
                <form id="uploadForm">
                    <div class="form-group">
                        <label>目标语言:</label>
                        <select id="langOut">
                            <option value="zh">中文</option>
                            <option value="en">英文</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>API地址:</label>
                        <input type="url" id="apiUrl" value="http://***********:3000/v1/" required>
                    </div>
                    
                    <div class="form-group">
                        <label>API密钥:</label>
                        <input type="password" id="apiKey" value="sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG" required>
                    </div>
                    
                    <button type="submit">开始翻译</button>
                </form>
                
                <div class="progress" id="progressSection" style="display: none;">
                    <h3>翻译进度</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="statusText">准备中...</p>
                </div>
                
                <div id="resultSection" style="display: none;">
                    <h3>翻译完成</h3>
                    <div id="downloadLinks"></div>
                </div>
            </div>
            
            <script>
                let selectedFile = null;
                let currentTaskId = null;
                let websocket = null;
                
                document.getElementById('fileInput').addEventListener('change', function(e) {
                    selectedFile = e.target.files[0];
                    if (selectedFile) {
                        document.querySelector('.upload-area p').textContent = '已选择: ' + selectedFile.name;
                    }
                });
                
                document.getElementById('uploadForm').addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    if (!selectedFile) {
                        alert('请先选择PDF文件');
                        return;
                    }
                    
                    const formData = new FormData();
                    formData.append('file', selectedFile);
                    formData.append('lang_out', document.getElementById('langOut').value);
                    formData.append('openai_base_url', document.getElementById('apiUrl').value);
                    formData.append('openai_api_key', document.getElementById('apiKey').value);
                    
                    try {
                        const response = await fetch('/upload', {
                            method: 'POST',
                            body: formData
                        });
                        
                        const result = await response.json();
                        currentTaskId = result.task_id;
                        
                        document.getElementById('progressSection').style.display = 'block';
                        connectWebSocket(currentTaskId);
                        
                    } catch (error) {
                        alert('上传失败: ' + error.message);
                    }
                });
                
                function connectWebSocket(taskId) {
                    const wsUrl = `ws://localhost:8000/ws/${taskId}`;
                    websocket = new WebSocket(wsUrl);
                    
                    websocket.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'progress_update') {
                            const progress = data.overall_progress || 0;
                            document.getElementById('progressFill').style.width = progress + '%';
                            document.getElementById('statusText').textContent = 
                                `${data.stage || '处理中'} - ${Math.round(progress)}%`;
                        } else if (data.type === 'complete') {
                            document.getElementById('resultSection').style.display = 'block';
                            document.getElementById('statusText').textContent = '翻译完成!';
                            
                            if (data.output_files) {
                                const linksDiv = document.getElementById('downloadLinks');
                                data.output_files.forEach(file => {
                                    const link = document.createElement('a');
                                    link.href = file.download_url;
                                    link.textContent = '下载 ' + file.name;
                                    link.style.display = 'block';
                                    link.style.margin = '10px 0';
                                    linksDiv.appendChild(link);
                                });
                            }
                        } else if (data.type === 'error') {
                            alert('翻译失败: ' + data.error);
                        }
                    };
                }
            </script>
        </body>
        </html>
        """)

@app.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    lang_out: str = Form("zh"),
    openai_base_url: str = Form("http://***********:3000/v1/"),
    openai_api_key: str = Form("sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG")
):
    """处理文件上传"""
    
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    task_id = str(uuid.uuid4())
    
    # 保存上传的文件
    upload_path = Path(f"uploads/{task_id}_{file.filename}")
    with open(upload_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # 创建输出目录
    output_dir = Path(f"outputs/{task_id}")
    output_dir.mkdir(exist_ok=True)
    
    # 启动模拟翻译任务
    translation_task = asyncio.create_task(
        simulate_translation(task_id, str(upload_path), str(output_dir))
    )
    
    translation_tasks[task_id] = translation_task
    
    return {"task_id": task_id, "message": "Translation started"}

async def simulate_translation(task_id: str, input_file: str, output_dir: str):
    """模拟翻译过程"""
    
    stages = [
        "ILCreater", "LayoutParser", "ParagraphFinder", 
        "StylesAndFormulas", "ILTranslator", "Typesetting", 
        "FontMapper", "PDFCreater"
    ]
    
    try:
        for i, stage in enumerate(stages):
            # 模拟每个阶段的进度
            for progress in range(0, 101, 20):
                overall_progress = (i * 100 + progress) / len(stages)
                
                await manager.send_message(task_id, {
                    "type": "progress_update",
                    "stage": stage,
                    "stage_progress": progress,
                    "overall_progress": overall_progress
                })
                
                await asyncio.sleep(0.5)  # 模拟处理时间
        
        # 模拟完成
        await manager.send_message(task_id, {
            "type": "complete",
            "message": "翻译完成！",
            "output_files": [
                {
                    "name": "翻译结果.pdf",
                    "download_url": f"/download/{task_id}/result.pdf"
                }
            ]
        })
        
    except Exception as e:
        await manager.send_message(task_id, {
            "type": "error",
            "error": str(e)
        })

@app.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket端点"""
    await manager.connect(websocket, task_id)
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(task_id)

@app.get("/download/{task_id}/{filename}")
async def download_file(task_id: str, filename: str):
    """下载文件（模拟）"""
    # 这里应该返回实际的翻译结果文件
    # 现在只是返回一个示例响应
    return {"message": f"Download {filename} for task {task_id}"}

if __name__ == "__main__":
    print("🌍 启动BabelDOC Web前端 (简化版)")
    print("📍 访问地址: http://localhost:8000")
    print("⏹️  按 Ctrl+C 停止服务器")
    
    uvicorn.run(
        "simple_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
