<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BabelDOC - PDF翻译工具</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🌍 BabelDOC PDF翻译工具</h1>
            <p>上传PDF文件，获得高质量的翻译结果</p>
        </header>

        <main>
            <!-- 文件上传区域 -->
            <section class="upload-section" id="uploadSection">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📄</div>
                    <h3>拖拽PDF文件到此处或点击选择</h3>
                    <p>支持PDF格式，最大文件大小100MB</p>
                    <input type="file" id="fileInput" accept=".pdf" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        选择文件
                    </button>
                </div>

                <!-- 翻译参数配置 -->
                <div class="config-section" id="configSection" style="display: none;">
                    <h3>翻译配置</h3>
                    <form id="translationForm">
                        <div class="form-group">
                            <label for="langOut">目标语言:</label>
                            <select id="langOut" name="lang_out">
                                <option value="zh">中文</option>
                                <option value="en">英文</option>
                                <option value="ja">日文</option>
                                <option value="ko">韩文</option>
                                <option value="fr">法文</option>
                                <option value="de">德文</option>
                                <option value="es">西班牙文</option>
                                <option value="ru">俄文</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="langIn">源语言:</label>
                            <select id="langIn" name="lang_in">
                                <option value="auto">自动检测</option>
                                <option value="en">英文</option>
                                <option value="zh">中文</option>
                                <option value="ja">日文</option>
                                <option value="ko">韩文</option>
                                <option value="fr">法文</option>
                                <option value="de">德文</option>
                                <option value="es">西班牙文</option>
                                <option value="ru">俄文</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="openaiModel">AI模型:</label>
                            <select id="openaiModel" name="openai_model">
                                <option value="qwen3-32b">Qwen3-32B</option>
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="openaiBaseUrl">API地址:</label>
                            <input type="url" id="openaiBaseUrl" name="openai_base_url" 
                                   value="http://10.0.51.251:3000/v1/" required>
                        </div>

                        <div class="form-group">
                            <label for="openaiApiKey">API密钥:</label>
                            <input type="password" id="openaiApiKey" name="openai_api_key" 
                                   value="sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG" required>
                        </div>

                        <button type="submit" class="translate-btn" id="translateBtn">
                            开始翻译
                        </button>
                    </form>
                </div>
            </section>

            <!-- 进度显示区域 -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <h3>翻译进度</h3>
                <div class="progress-info">
                    <div class="status-message" id="statusMessage">准备开始翻译...</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">0%</div>
                    </div>
                    <div class="stage-info" id="stageInfo">
                        <div class="current-stage" id="currentStage">等待开始...</div>
                        <div class="stage-progress" id="stageProgress">0/0</div>
                    </div>
                </div>
            </section>

            <!-- 结果显示区域 -->
            <section class="result-section" id="resultSection" style="display: none;">
                <h3>翻译完成</h3>
                <div class="result-info">
                    <div class="success-message" id="successMessage">
                        ✅ 翻译已完成！
                    </div>
                    <div class="result-stats" id="resultStats">
                        <div class="stat-item">
                            <span class="stat-label">耗时:</span>
                            <span class="stat-value" id="totalTime">-</span>
                        </div>
                    </div>
                    <div class="download-section" id="downloadSection">
                        <h4>下载文件:</h4>
                        <div class="download-links" id="downloadLinks">
                            <!-- 下载链接将通过JavaScript动态添加 -->
                        </div>
                    </div>
                    <button class="new-translation-btn" onclick="resetForm()">
                        翻译新文件
                    </button>
                </div>
            </section>

            <!-- 错误显示区域 -->
            <section class="error-section" id="errorSection" style="display: none;">
                <h3>翻译失败</h3>
                <div class="error-info">
                    <div class="error-message" id="errorMessage">
                        ❌ 翻译过程中出现错误
                    </div>
                    <div class="error-details" id="errorDetails">
                        <!-- 错误详情将通过JavaScript显示 -->
                    </div>
                    <button class="retry-btn" onclick="resetForm()">
                        重新开始
                    </button>
                </div>
            </section>
        </main>

        <footer>
            <p>Powered by <a href="https://github.com/funstory-ai/BabelDOC" target="_blank">BabelDOC</a></p>
        </footer>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
