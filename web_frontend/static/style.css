/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主要内容区域 */
main {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

section {
    padding: 30px;
}

/* 上传区域样式 */
.upload-area {
    border: 3px dashed #ddd;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #f0f4ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-area h3 {
    margin-bottom: 10px;
    color: #333;
}

.upload-area p {
    color: #666;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 配置区域样式 */
.config-section {
    border-top: 1px solid #eee;
    margin-top: 30px;
    padding-top: 30px;
}

.config-section h3 {
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.translate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.translate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.translate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 进度区域样式 */
.progress-section {
    border-top: 1px solid #eee;
}

.progress-info {
    text-align: center;
}

.status-message {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: #555;
}

.progress-bar-container {
    position: relative;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: #333;
    font-size: 0.9rem;
}

.stage-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.current-stage {
    font-weight: 600;
    color: #333;
}

.stage-progress {
    color: #666;
    font-size: 0.9rem;
}

/* 结果区域样式 */
.result-section {
    border-top: 1px solid #eee;
    text-align: center;
}

.success-message {
    font-size: 1.2rem;
    color: #28a745;
    margin-bottom: 20px;
}

.result-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

.download-section h4 {
    margin-bottom: 15px;
    color: #333;
}

.download-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 30px;
}

.download-link {
    display: inline-block;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-decoration: none;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.download-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.new-translation-btn,
.retry-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.new-translation-btn:hover,
.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}

/* 错误区域样式 */
.error-section {
    border-top: 1px solid #eee;
    text-align: center;
}

.error-message {
    font-size: 1.2rem;
    color: #dc3545;
    margin-bottom: 20px;
}

.error-details {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: left;
    font-family: monospace;
    font-size: 0.9rem;
}

/* 底部样式 */
footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

footer a {
    color: white;
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    section {
        padding: 20px;
    }
    
    .result-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .download-links {
        gap: 8px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}
