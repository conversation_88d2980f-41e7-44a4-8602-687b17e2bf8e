// 全局变量
let selectedFile = null;
let currentTaskId = null;
let websocket = null;

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const configSection = document.getElementById('configSection');
const translationForm = document.getElementById('translationForm');
const uploadSection = document.getElementById('uploadSection');
const progressSection = document.getElementById('progressSection');
const resultSection = document.getElementById('resultSection');
const errorSection = document.getElementById('errorSection');

// 进度相关元素
const statusMessage = document.getElementById('statusMessage');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const currentStage = document.getElementById('currentStage');
const stageProgress = document.getElementById('stageProgress');

// 结果相关元素
const successMessage = document.getElementById('successMessage');
const totalTime = document.getElementById('totalTime');
const downloadLinks = document.getElementById('downloadLinks');

// 错误相关元素
const errorMessage = document.getElementById('errorMessage');
const errorDetails = document.getElementById('errorDetails');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // 文件输入事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // 表单提交事件
    translationForm.addEventListener('submit', handleFormSubmit);
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processSelectedFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processSelectedFile(files[0]);
    }
}

function processSelectedFile(file) {
    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.pdf')) {
        alert('请选择PDF文件！');
        return;
    }
    
    // 验证文件大小 (100MB)
    if (file.size > 100 * 1024 * 1024) {
        alert('文件大小不能超过100MB！');
        return;
    }
    
    selectedFile = file;
    
    // 更新UI显示选中的文件
    uploadArea.innerHTML = `
        <div class="upload-icon">✅</div>
        <h3>已选择文件: ${file.name}</h3>
        <p>文件大小: ${formatFileSize(file.size)}</p>
        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
            重新选择
        </button>
    `;
    
    // 显示配置区域
    configSection.style.display = 'block';
    configSection.classList.add('fade-in');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!selectedFile) {
        alert('请先选择PDF文件！');
        return;
    }
    
    // 禁用提交按钮
    const submitBtn = document.getElementById('translateBtn');
    submitBtn.disabled = true;
    submitBtn.textContent = '正在上传...';
    
    try {
        // 准备表单数据
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('openai_model', document.getElementById('openaiModel').value);
        formData.append('openai_base_url', document.getElementById('openaiBaseUrl').value);
        formData.append('openai_api_key', document.getElementById('openaiApiKey').value);
        formData.append('lang_out', document.getElementById('langOut').value);
        formData.append('lang_in', document.getElementById('langIn').value);
        
        // 上传文件并开始翻译
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        currentTaskId = result.task_id;
        
        // 切换到进度显示
        showProgressSection();
        
        // 建立WebSocket连接
        connectWebSocket(currentTaskId);
        
    } catch (error) {
        console.error('Upload error:', error);
        showError('上传失败: ' + error.message);
        
        // 重新启用提交按钮
        submitBtn.disabled = false;
        submitBtn.textContent = '开始翻译';
    }
}

function showProgressSection() {
    uploadSection.style.display = 'none';
    progressSection.style.display = 'block';
    progressSection.classList.add('fade-in');
    
    // 重置进度显示
    updateProgress(0, '准备开始翻译...', '等待开始...', '0/0');
}

function connectWebSocket(taskId) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/${taskId}`;
    
    websocket = new WebSocket(wsUrl);
    
    websocket.onopen = function(event) {
        console.log('WebSocket connected');
        statusMessage.textContent = '已连接到服务器，等待翻译开始...';
    };
    
    websocket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleProgressUpdate(data);
    };
    
    websocket.onclose = function(event) {
        console.log('WebSocket disconnected');
    };
    
    websocket.onerror = function(error) {
        console.error('WebSocket error:', error);
        showError('连接错误，请刷新页面重试');
    };
}

function handleProgressUpdate(data) {
    console.log('Progress update:', data);
    
    switch (data.type) {
        case 'status':
            statusMessage.textContent = data.message;
            break;
            
        case 'progress_start':
            updateProgress(
                data.overall_progress || 0,
                `开始阶段: ${data.stage}`,
                data.stage,
                `${data.stage_current}/${data.stage_total}`
            );
            break;
            
        case 'progress_update':
            updateProgress(
                data.overall_progress || 0,
                `正在处理: ${data.stage}`,
                data.stage,
                `${data.stage_current}/${data.stage_total}`
            );
            break;
            
        case 'progress_end':
            updateProgress(
                data.overall_progress || 0,
                `完成阶段: ${data.stage}`,
                data.stage,
                `${data.stage_current}/${data.stage_total}`
            );
            break;
            
        case 'complete':
            showResult(data);
            break;
            
        case 'finish':
            // 处理翻译完成
            if (data.translate_result) {
                showResult({
                    message: '翻译完成！',
                    output_files: [],
                    total_time: data.translate_result.total_seconds || 0
                });
            }
            break;
            
        case 'error':
            showError('翻译失败: ' + (data.error || '未知错误'));
            break;
    }
}

function updateProgress(percentage, message, stage, stageProgressText) {
    // 更新进度条
    progressFill.style.width = `${Math.min(percentage, 100)}%`;
    progressText.textContent = `${Math.round(percentage)}%`;
    
    // 更新状态信息
    statusMessage.textContent = message;
    currentStage.textContent = stage;
    stageProgress.textContent = stageProgressText;
}

function showResult(data) {
    progressSection.style.display = 'none';
    resultSection.style.display = 'block';
    resultSection.classList.add('fade-in');
    
    // 更新成功消息
    successMessage.textContent = data.message || '翻译完成！';
    
    // 更新统计信息
    if (data.total_time) {
        totalTime.textContent = formatTime(data.total_time);
    }
    
    // 更新下载链接
    if (data.output_files && data.output_files.length > 0) {
        downloadLinks.innerHTML = '';
        data.output_files.forEach(file => {
            const link = document.createElement('a');
            link.href = file.download_url;
            link.className = 'download-link';
            link.textContent = `📄 下载 ${file.name}`;
            link.download = true;
            downloadLinks.appendChild(link);
        });
    } else {
        downloadLinks.innerHTML = '<p>暂无可下载文件</p>';
    }
    
    // 关闭WebSocket连接
    if (websocket) {
        websocket.close();
        websocket = null;
    }
}

function showError(message) {
    progressSection.style.display = 'none';
    errorSection.style.display = 'block';
    errorSection.classList.add('fade-in');
    
    errorMessage.textContent = '❌ ' + message;
    errorDetails.textContent = message;
    
    // 关闭WebSocket连接
    if (websocket) {
        websocket.close();
        websocket = null;
    }
}

function formatTime(seconds) {
    if (seconds < 60) {
        return `${Math.round(seconds)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${minutes}分钟`;
    }
}

function resetForm() {
    // 重置所有状态
    selectedFile = null;
    currentTaskId = null;
    
    if (websocket) {
        websocket.close();
        websocket = null;
    }
    
    // 重置UI
    uploadSection.style.display = 'block';
    progressSection.style.display = 'none';
    resultSection.style.display = 'none';
    errorSection.style.display = 'none';
    configSection.style.display = 'none';
    
    // 重置上传区域
    uploadArea.innerHTML = `
        <div class="upload-icon">📄</div>
        <h3>拖拽PDF文件到此处或点击选择</h3>
        <p>支持PDF格式，最大文件大小100MB</p>
        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
            选择文件
        </button>
    `;
    
    // 重置表单
    translationForm.reset();
    document.getElementById('translateBtn').disabled = false;
    document.getElementById('translateBtn').textContent = '开始翻译';
    
    // 重置文件输入
    fileInput.value = '';
}
