import asyncio
import json
import logging
import os
import uuid
from pathlib import Path
from typing import Dict, Optional

import uvicorn
from fastapi import Fast<PERSON><PERSON>, File, Form, HTTPException, UploadFile, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles

# Import BabelDOC modules
import sys
sys.path.append(str(Path(__file__).parent.parent))

try:
    import babeldoc.format.pdf.high_level
    from babeldoc.format.pdf.translation_config import TranslationConfig
    from babeldoc.translator.translator import OpenAITranslator
    from babeldoc.translator.translator import set_translate_rate_limiter
except ImportError as e:
    logger.error(f"Failed to import BabelDOC modules: {e}")
    logger.error("Please make sure BabelDOC is properly installed")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="BabelDOC Web Frontend", version="1.0.0")

# Mount static files
app.mount("/static", StaticFiles(directory="web_frontend/static"), name="static")

# Global variables for managing translation tasks
translation_tasks: Dict[str, asyncio.Task] = {}
websocket_connections: Dict[str, WebSocket] = {}

# Ensure directories exist
os.makedirs("web_frontend/uploads", exist_ok=True)
os.makedirs("web_frontend/outputs", exist_ok=True)


class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, task_id: str):
        await websocket.accept()
        self.active_connections[task_id] = websocket

    def disconnect(self, task_id: str):
        if task_id in self.active_connections:
            del self.active_connections[task_id]

    async def send_message(self, task_id: str, message: dict):
        if task_id in self.active_connections:
            try:
                await self.active_connections[task_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {task_id}: {e}")
                self.disconnect(task_id)


manager = ConnectionManager()


@app.get("/")
async def read_root():
    """Serve the main page"""
    return FileResponse("web_frontend/static/index.html")


@app.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    openai_model: str = Form("qwen3-32b"),
    openai_base_url: str = Form("http://10.0.51.251:3000/v1/"),
    openai_api_key: str = Form("sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG"),
    lang_out: str = Form("zh"),
    lang_in: str = Form("auto")
):
    """Handle file upload and start translation"""

    # Validate file type
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")

    # Generate unique task ID
    task_id = str(uuid.uuid4())

    # Save uploaded file
    upload_path = Path(f"web_frontend/uploads/{task_id}_{file.filename}")
    with open(upload_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    # Create output directory for this task
    output_dir = Path(f"web_frontend/outputs/{task_id}")
    output_dir.mkdir(exist_ok=True)

    # Start translation task
    translation_task = asyncio.create_task(
        translate_pdf(
            task_id=task_id,
            input_file=str(upload_path),
            output_dir=str(output_dir),
            openai_model=openai_model,
            openai_base_url=openai_base_url,
            openai_api_key=openai_api_key,
            lang_out=lang_out,
            lang_in=lang_in
        )
    )

    translation_tasks[task_id] = translation_task

    return {"task_id": task_id, "message": "Translation started"}


async def translate_pdf(
    task_id: str,
    input_file: str,
    output_dir: str,
    openai_model: str,
    openai_base_url: str,
    openai_api_key: str,
    lang_out: str,
    lang_in: str
):
    """Perform PDF translation with progress reporting"""

    try:
        # Send initial status
        await manager.send_message(task_id, {
            "type": "status",
            "message": "Initializing translation..."
        })

        # Create translator
        translator = OpenAITranslator(
            lang_in=lang_in,
            lang_out=lang_out,
            model=openai_model,
            base_url=openai_base_url,
            api_key=openai_api_key,
            ignore_cache=False,
        )

        # Set translation rate limiter
        set_translate_rate_limiter(1)

        # Initialize document layout model
        from babeldoc.docvision.doclayout import DocLayoutModel
        doc_layout_model = DocLayoutModel.load_onnx()

        # Create translation config
        config = TranslationConfig(
            input_file=input_file,
            font=None,
            pages=None,
            output_dir=output_dir,
            translator=translator,
            debug=False,
            lang_in=lang_in,
            lang_out=lang_out,
            no_dual=False,
            no_mono=False,
            qps=1,
            doc_layout_model=doc_layout_model,
            report_interval=0.5  # Report progress every 0.5 seconds
        )

        # Start translation with progress monitoring
        await manager.send_message(task_id, {
            "type": "status",
            "message": "Starting translation process..."
        })

        async for event in babeldoc.format.pdf.high_level.async_translate(config):
            # Forward progress events to WebSocket
            await manager.send_message(task_id, event)

            if event["type"] == "error":
                logger.error(f"Translation error for task {task_id}: {event['error']}")
                break
            elif event["type"] == "finish":
                result = event["translate_result"]
                logger.info(f"Translation completed for task {task_id}")

                # Send completion message with result files
                output_files = []
                if hasattr(result, 'dual_pdf_path') and result.dual_pdf_path:
                    output_files.append({
                        "name": "Dual Language PDF",
                        "path": str(result.dual_pdf_path),
                        "download_url": f"/download/{task_id}/{Path(result.dual_pdf_path).name}"
                    })
                if hasattr(result, 'mono_pdf_path') and result.mono_pdf_path:
                    output_files.append({
                        "name": "Translated PDF",
                        "path": str(result.mono_pdf_path),
                        "download_url": f"/download/{task_id}/{Path(result.mono_pdf_path).name}"
                    })

                await manager.send_message(task_id, {
                    "type": "complete",
                    "message": "Translation completed successfully!",
                    "output_files": output_files,
                    "total_time": getattr(result, 'total_seconds', 0)
                })
                break

    except Exception as e:
        logger.error(f"Translation failed for task {task_id}: {str(e)}")
        await manager.send_message(task_id, {
            "type": "error",
            "error": str(e)
        })
    finally:
        # Clean up
        if task_id in translation_tasks:
            del translation_tasks[task_id]


@app.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket endpoint for real-time progress updates"""
    await manager.connect(websocket, task_id)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(task_id)


@app.get("/download/{task_id}/{filename}")
async def download_file(task_id: str, filename: str):
    """Download translated files"""
    file_path = Path(f"web_frontend/outputs/{task_id}/{filename}")

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(
        path=str(file_path),
        filename=filename,
        media_type='application/pdf'
    )


@app.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """Get current status of a translation task"""
    if task_id in translation_tasks:
        task = translation_tasks[task_id]
        if task.done():
            return {"status": "completed"}
        else:
            return {"status": "running"}
    else:
        return {"status": "not_found"}


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
